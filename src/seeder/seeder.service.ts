import { Injectable } from '@nestjs/common';
import { QuidaxService } from '../../libs/quidax/src';
import { DataSource, Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Network } from '../crypto/entities/network.entity';
import { Currency } from '../crypto/entities/currency.entity';
import { InternalCacheService } from '@app/internal-cache';
import { ErrorHandlingService } from '../utils/error-handling';

@Injectable()
export class SeederService {
  constructor(
    private readonly quidaxService: QuidaxService,
    private readonly dataSource: DataSource,
    private readonly internalCacheService: InternalCacheService,
    @InjectRepository(Currency)
    private readonly currencyRepository: Repository<Currency>,
    @InjectRepository(Network)
    private readonly networkRepository: Repository<Network>,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  async seed() {
    const context = this.errorHandlingService.createContext(
      'SeederService',
      'seed',
    );

    const queryRunner = this.dataSource.createQueryRunner();

    try {
      await queryRunner.connect();
      await queryRunner.startTransaction();

      const apiResponse = await this.quidaxService.fetchUserWallets('me');

      for (const data of apiResponse) {
        try {
          await this.processCurrencyData(data, context);
        } catch (currencyError) {
          this.errorHandlingService.handleError(
            currencyError,
            {
              ...context,
              metadata: { currencyCode: data.currency, currencyId: data.id },
            },
            {
              shouldThrow: false,
              customMessage: `Failed to process currency: ${data.currency}`,
            },
          );
          // Continue with next currency instead of failing entire seed
        }
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      this.errorHandlingService.handleError(error, context, {
        shouldThrow: false,
        customMessage: 'Failed to seed currency and network data',
      });
    } finally {
      await queryRunner.release();
    }
  }

  private async processCurrencyData(data: any, context: any) {
    const currencyCacheKey = `crypto:currency_${data.currency}`;

    let currency =
      await this.internalCacheService.get<Currency>(currencyCacheKey);
    if (!currency) {
      currency = await this.currencyRepository.findOne({
        where: { currencyCode: data.currency },
      });
      if (!currency) {
        currency = this.currencyRepository.create({
          id: data.id,
          name: data.name,
          currencyCode: data.currency,
          isCrypto: data.is_crypto,
          referenceCurrency: data.reference_currency,
          blockchainEnabled: data.blockchain_enabled,
          defaultNetwork: data.default_network,
          networks: [],
        });
        await this.currencyRepository.save(currency);
      }

      for (const net of data.networks) {
        try {
          let network = await this.networkRepository.findOne({
            where: { id: net.id },
          });

          const networkData = {
            id: net.id,
            name: net.name,
            depositsEnabled: net.deposits_enabled,
            withdrawsEnabled: net.withdraws_enabled,
          };

          if (!network) {
            network = this.networkRepository.create(networkData);
            await this.networkRepository.save(network);
          }

          if (!currency.networks) {
            currency.networks = [];
          }
          currency.networks.push(network);

          // Cache the updated currency
          try {
            await this.internalCacheService
              .tags('crypto', 'currencies')
              .set(currencyCacheKey, currency);
          } catch (cacheError) {
            this.errorHandlingService.handleCacheError(cacheError, context, {
              customMessage: `Failed to cache currency: ${data.currency}`,
            });
          }

          const currencyWithNetworks = await this.currencyRepository.findOne({
            where: { id: currency.id },
            relations: ['networks'],
          });

          if (
            currencyWithNetworks &&
            !currencyWithNetworks.networks.some((n) => n.id === network.id)
          ) {
            currencyWithNetworks.networks.push(network);
            await this.currencyRepository.save(currencyWithNetworks);
          }
        } catch (networkError) {
          this.errorHandlingService.handleDatabaseError(
            networkError,
            {
              ...context,
              metadata: { networkId: net.id, networkName: net.name },
            },
            {
              shouldThrow: false,
              customMessage: `Failed to process network: ${net.name}`,
            },
          );
        }
      }
    }
  }
}
