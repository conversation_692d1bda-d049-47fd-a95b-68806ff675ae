import {
  Injectable,
  Logger,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
  UnprocessableEntityException,
  ServiceUnavailableException,
} from '@nestjs/common';
import { AxiosError } from 'axios';
import { QueryFailedError } from 'typeorm';
import { randomUUID } from 'crypto';

export interface ErrorContext {
  service: string;
  method: string;
  userId?: string;
  correlationId?: string;
  metadata?: Record<string, any>;
}

export interface ErrorHandlingOptions {
  shouldThrow?: boolean;
  logLevel?: 'error' | 'warn' | 'debug';
  includeStack?: boolean;
  customMessage?: string;
}

@Injectable()
export class ErrorHandlingService {
  private readonly logger = new Logger(ErrorHandlingService.name);

  /**
   * Handle and transform errors with consistent logging and exception throwing
   */
  handleError(
    error: any,
    context: ErrorContext,
    options: ErrorHandlingOptions = {},
  ): never | void {
    const {
      shouldThrow = true,
      logLevel = 'error',
      includeStack = true,
      customMessage,
    } = options;

    const correlationId = context.correlationId || randomUUID();
    const errorMessage = customMessage || this.extractErrorMessage(error);

    // Create comprehensive log context
    const logContext = {
      correlationId,
      service: context.service,
      method: context.method,
      userId: context.userId,
      errorType: error.constructor.name,
      originalMessage: error.message,
      metadata: context.metadata,
    };

    // Log error with appropriate level
    const logMessage = `Error in ${context.service}.${context.method}: ${errorMessage}`;

    switch (logLevel) {
      case 'error':
        this.logger.error(
          logMessage,
          includeStack ? error.stack : undefined,
          logContext,
        );
        break;
      case 'warn':
        this.logger.warn(logMessage, logContext);
        break;
      case 'debug':
        this.logger.debug(logMessage, logContext);
        break;
    }

    if (shouldThrow) {
      const transformedException = this.transformError(
        error,
        correlationId,
        customMessage,
      );
      throw transformedException;
    }
  }

  /**
   * Handle external API errors (Quidax, etc.)
   */
  handleExternalApiError(
    error: AxiosError,
    context: ErrorContext,
    options: ErrorHandlingOptions = {},
  ): never | void {
    const apiErrorData = error.response?.data as any;
    const statusCode = error.response?.status;

    // Enhanced context for API errors
    const enhancedContext = {
      ...context,
      metadata: {
        ...context.metadata,
        apiUrl: error.config?.url,
        apiMethod: error.config?.method,
        statusCode,
        apiErrorData,
      },
    };

    // Determine appropriate exception type based on status code
    let customMessage = options.customMessage;
    if (!customMessage && apiErrorData) {
      customMessage =
        apiErrorData.message || apiErrorData.error || 'External API error';
    }

    this.handleError(error, enhancedContext, {
      ...options,
      customMessage,
      shouldThrow: true,
    });
  }

  /**
   * Handle database errors with transaction context
   */
  handleDatabaseError(
    error: any,
    context: ErrorContext,
    options: ErrorHandlingOptions = {},
  ): never | void {
    const enhancedContext = {
      ...context,
      metadata: {
        ...context.metadata,
        isDatabaseError: true,
        errorCode: error.code,
        sqlState: error.sqlState,
      },
    };

    let customMessage = options.customMessage;
    if (!customMessage) {
      if (error instanceof QueryFailedError) {
        customMessage = 'Database operation failed';
      } else {
        customMessage = 'Database error occurred';
      }
    }

    this.handleError(error, enhancedContext, {
      ...options,
      customMessage,
    });
  }

  /**
   * Handle Redis/Cache errors
   */
  handleCacheError(
    error: any,
    context: ErrorContext,
    options: ErrorHandlingOptions = {},
  ): never | void {
    const enhancedContext = {
      ...context,
      metadata: {
        ...context.metadata,
        isCacheError: true,
        cacheOperation: context.metadata?.operation,
      },
    };

    this.handleError(error, enhancedContext, {
      logLevel: 'warn', // Cache errors are often non-critical
      shouldThrow: false, // Don't throw by default for cache errors
      customMessage: options.customMessage || 'Cache operation failed',
      ...options,
    });
  }

  /**
   * Create error context helper
   */
  createContext(
    service: string,
    method: string,
    userId?: string,
    metadata?: Record<string, any>,
  ): ErrorContext {
    return {
      service,
      method,
      userId,
      correlationId: randomUUID(),
      metadata,
    };
  }

  /**
   * Extract meaningful error message from various error types
   */
  private extractErrorMessage(error: any): string {
    if (typeof error === 'string') {
      return error;
    }

    if (error.message) {
      return error.message;
    }

    if (error.response?.data?.message) {
      return error.response.data.message;
    }

    if (error.response?.data?.error) {
      return error.response.data.error;
    }

    return 'An unexpected error occurred';
  }

  /**
   * Transform errors into appropriate NestJS exceptions
   */
  private transformError(
    error: any,
    correlationId: string,
    customMessage?: string,
  ): Error {
    const message = customMessage || this.extractErrorMessage(error);
    const errorWithCorrelation = `${message} (ID: ${correlationId})`;

    // Handle Axios errors
    if (error.isAxiosError || error instanceof AxiosError) {
      const statusCode = error.response?.status;

      switch (statusCode) {
        case 400:
          return new BadRequestException(errorWithCorrelation);
        case 404:
          return new NotFoundException(errorWithCorrelation);
        case 422:
          return new UnprocessableEntityException(errorWithCorrelation);
        case 503:
        case 502:
        case 504:
          return new ServiceUnavailableException(errorWithCorrelation);
        default:
          return new InternalServerErrorException(errorWithCorrelation);
      }
    }

    // Handle database errors
    if (error instanceof QueryFailedError) {
      if (error.message.includes('Duplicate entry')) {
        return new BadRequestException(
          `Duplicate entry: ${errorWithCorrelation}`,
        );
      }
      if (error.message.includes('foreign key constraint')) {
        return new BadRequestException(
          `Invalid reference: ${errorWithCorrelation}`,
        );
      }
      return new InternalServerErrorException(errorWithCorrelation);
    }

    // Handle NestJS exceptions (pass through)
    if (
      error instanceof BadRequestException ||
      error instanceof NotFoundException ||
      error instanceof UnprocessableEntityException ||
      error instanceof InternalServerErrorException
    ) {
      return error;
    }

    // Default to internal server error
    return new InternalServerErrorException(errorWithCorrelation);
  }
}
