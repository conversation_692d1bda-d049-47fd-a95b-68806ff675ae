import { Test, TestingModule } from '@nestjs/testing';
import { ErrorHandlingService } from './error-handling.service';
import { BadRequestException, NotFoundException, InternalServerErrorException } from '@nestjs/common';
import { AxiosError } from 'axios';

describe('ErrorHandlingService', () => {
  let service: ErrorHandlingService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ErrorHandlingService],
    }).compile();

    service = module.get<ErrorHandlingService>(ErrorHandlingService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createContext', () => {
    it('should create error context with correlation ID', () => {
      const context = service.createContext('TestService', 'testMethod', 'user123', { test: 'data' });
      
      expect(context.service).toBe('TestService');
      expect(context.method).toBe('testMethod');
      expect(context.userId).toBe('user123');
      expect(context.metadata).toEqual({ test: 'data' });
      expect(context.correlationId).toBeDefined();
    });
  });

  describe('handleExternalApiError', () => {
    it('should transform 400 status to BadRequestException', () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 400,
          data: { message: 'Bad request' }
        }
      } as AxiosError;

      const context = service.createContext('TestService', 'testMethod');

      expect(() => {
        service.handleExternalApiError(axiosError, context);
      }).toThrow(BadRequestException);
    });

    it('should transform 404 status to NotFoundException', () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 404,
          data: { message: 'Not found' }
        }
      } as AxiosError;

      const context = service.createContext('TestService', 'testMethod');

      expect(() => {
        service.handleExternalApiError(axiosError, context);
      }).toThrow(NotFoundException);
    });

    it('should transform unknown status to InternalServerErrorException', () => {
      const axiosError = {
        isAxiosError: true,
        response: {
          status: 500,
          data: { message: 'Server error' }
        }
      } as AxiosError;

      const context = service.createContext('TestService', 'testMethod');

      expect(() => {
        service.handleExternalApiError(axiosError, context);
      }).toThrow(InternalServerErrorException);
    });
  });

  describe('handleCacheError', () => {
    it('should not throw by default for cache errors', () => {
      const cacheError = new Error('Redis connection failed');
      const context = service.createContext('TestService', 'testMethod');

      expect(() => {
        service.handleCacheError(cacheError, context);
      }).not.toThrow();
    });

    it('should throw when shouldThrow is true', () => {
      const cacheError = new Error('Redis connection failed');
      const context = service.createContext('TestService', 'testMethod');

      expect(() => {
        service.handleCacheError(cacheError, context, { shouldThrow: true });
      }).toThrow(InternalServerErrorException);
    });
  });

  describe('handleDatabaseError', () => {
    it('should handle database errors with custom message', () => {
      const dbError = new Error('Database connection failed');
      const context = service.createContext('TestService', 'testMethod');

      expect(() => {
        service.handleDatabaseError(dbError, context, { 
          customMessage: 'Custom DB error message' 
        });
      }).toThrow(InternalServerErrorException);
    });
  });
});
