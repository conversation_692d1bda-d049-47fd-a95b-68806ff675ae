import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { OrderRepository } from '../repositories/orders.repository';
import { QuidaxService } from '@app/quidax';
import { CreateOrderDto } from '../dtos/orders.dto';
import { randomUUID } from 'crypto';
import { AuthData } from '@crednet/authmanager';
import { UserRepository } from '../repositories/users.repository';
import { Orders, OrderStatus } from '../entities/orders.entity';
import { TradeRepository } from '../repositories/trades.repository';

@Injectable()
export class OrdersService {
  constructor(
    private readonly orderRepository: OrderRepository,
    private readonly quidaxService: QuidaxService,
    private readonly userRepository: UserRepository,
    private readonly tradeRepository: TradeRepository,
  ) {}

  async createOrder(auth: AuthData, createOrderDto: CreateOrderDto) {
    const isKycVerified = true;
    if (!isKycVerified) {
      throw new BadRequestException('KYC not verified');
    }
    const reference = 'cp_ord_' + randomUUID();

    const user = await this.userRepository.getUserByUserId(auth.id.toString());

    let response: any;

    if (createOrderDto.orderType === 'limit') {
      response = await this.quidaxService.createOrder(user.id, {
        market: createOrderDto.baseCurrency + createOrderDto.quoteCurrency,
        side: createOrderDto.side,
        price: createOrderDto.price,
        volume: createOrderDto.volume,
        ord_type: createOrderDto.orderType,
        reference,
      });
    } else {
      response = await this.quidaxService.createOrder(user.id, {
        market: createOrderDto.baseCurrency + createOrderDto.quoteCurrency,
        side: createOrderDto.side,
        volume: createOrderDto.volume,
        ord_type: createOrderDto.orderType,
        reference,
      });
    }

    if (response.status === 'error') {
      throw new BadRequestException(response.message);
    }

    const order = await this.orderRepository.createOrder({
      id: response.data.id,
      reference: response.data.reference || reference,
      marketId: response.data.market.id,
      baseUnit: response.data.market.base_unit,
      quoteUnit: response.data.market.quote_unit,
      side: response.data.side,
      orderType: response.data.order_type,
      priceUnit: response.data.price.unit,
      priceAmount: response.data.price.amount,
      avgPriceUnit: response.data.avg_price.unit,
      avgPriceAmount: response.data.avg_price.amount,
      volumeUnit: response.data.volume.unit,
      volumeAmount: response.data.volume.amount,
      originVolumeUnit: response.data.origin_volume.unit,
      originVolumeAmount: response.data.origin_volume.amount,
      executedVolumeUnit: response.data.executed_volume.unit,
      executedVolumeAmount: response.data.executed_volume.amount,
      status: response.data.status,
      tradesCount: response.data.trades_count,
      userId: user.id,
    });

    return order;
  }

  async cancelOrder(auth: AuthData, orderId: string) {
    const order = await this.orderRepository.getOrder(orderId);
    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }

    const user = await this.userRepository.getUserByUserId(auth.id.toString());

    const response = await this.quidaxService.cancelOrder(user.id, orderId);

    if (response.status === 'error') {
      throw new BadRequestException(response.message);
    }

    const updatedOrder = await this.orderRepository.update(
      {
        id: orderId,
      },
      {
        status: response.data.status,
      },
    );

    return updatedOrder;
  }

  async getOrder(orderId: string): Promise<Orders> {
    const order = await this.orderRepository.getOrder(orderId);
    if (!order) {
      throw new NotFoundException(`Order with ID ${orderId} not found`);
    }
    return order;
  }

  async getOrdersByUserId(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ) {
    const user = await this.userRepository.getUserByUserId(userId);

    const { orders, total } = await this.orderRepository.getOrdersByUserId(
      user.id,
      startDate,
      endDate,
      page,
      limit,
    );
    return { orders, total, page, limit };
  }

  async updateOrderByReference(reference: string, data: any): Promise<Orders> {
    const order = await this.orderRepository.findOne({
      where: { reference },
    });
    order.tradesCount = data.trades_count;
    order.status = data.status;

    return await this.orderRepository.save(order);
  }

  async verifyOrderStatus(orderId: string): Promise<any> {
    const order = await this.orderRepository.getOrder(orderId);

    const fetchOrder = await this.quidaxService.fetchOrderDetails(
      order.user.id,
      orderId,
    );

    const orderStatus = fetchOrder.status;

    this.updateOrderByReference(order.reference, fetchOrder);

    // todo add notifications for each status change case

    if (orderStatus === 'done' && fetchOrder.trades_count > 0) {
      // Handle order completed event
      for (const trade of fetchOrder.trades) {
        await this.tradeRepository.save({
          id: trade.id,
          marketId: trade.market.id,
          marketBaseUnit: trade.market.base_unit,
          marketQuoteUnit: trade.market.quote_unit,
          priceAmount: trade.price.amount,
          priceUnit: trade.price.unit,
          volumeAmount: trade.volume.amount,
          volumeUnit: trade.volume.unit,
          totalAmount: trade.total.amount,
          totalUnit: trade.total.unit,
          order,
        });
      }
    }
  }

  async requeryOrderStatus(pageNumber: number) {
    console.log('running job:: verifyOrderStatus ', pageNumber);

    const items = await this.orderRepository.findMany(
      {
        page: pageNumber,
        limit: 10,
      },
      {
        where: {
          status: OrderStatus.WAIT,
        },
        relations: ['user'],
      },
    );

    for (const item of items.items) {
      await this.verifyOrderStatus(item.id);
    }

    if (pageNumber < items.meta.totalPages) {
      return this.requeryOrderStatus(++pageNumber);
    }
  }
}
