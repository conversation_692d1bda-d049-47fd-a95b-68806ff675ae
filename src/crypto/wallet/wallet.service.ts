import {
  Injectable,
  BadRequestException,
  NotFoundException,
} from '@nestjs/common';
import { WalletRepository } from '../repositories/wallet.repository';
import { Wallet } from '../entities/wallet.entity';
import { QuidaxService } from '@app/quidax';
import { CurrencyRepository } from '../repositories/currency.repository';
import { Currency } from '../entities/currency.entity';
import { InternalCacheService } from '@app/internal-cache';
import { AddressRepository } from '../repositories/address.repository';
import { AddressStatus } from '../entities/address.entity';
import { ErrorHandlingService } from '../../utils/error-handling';

@Injectable()
export class WalletService {
  constructor(
    private readonly walletRepository: WalletRepository,
    private readonly quidaxService: QuidaxService,
    private readonly currencyRepository: CurrencyRepository,
    private readonly internalCacheService: InternalCacheService,
    private readonly addressRepository: AddressRepository,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  async createWallet(
    id: string,
    userId: string,
    currency: string,
    address: string,
    destinationTag: string,
  ): Promise<Wallet> {
    const context = this.errorHandlingService.createContext(
      'WalletService',
      'createWallet',
      userId,
      { id, currency, address, destinationTag },
    );

    try {
      await this.validateCurrency(currency);

      // Update address status
      try {
        await this.addressRepository.update(
          { id: id },
          {
            status: AddressStatus.SUCCESS,
            address: address,
            destinationTag: destinationTag,
          },
        );
      } catch (dbError) {
        this.errorHandlingService.handleDatabaseError(dbError, {
          ...context,
          metadata: { ...context.metadata, operation: 'updateAddress' },
        });
      }

      const getWallet = await this.walletRepository.getUserWalletByCurrency(
        userId,
        currency,
      );
      if (getWallet) return getWallet;

      // Fetch wallet from external API
      let result: any;
      try {
        result = await this.quidaxService.fetchWallet(userId, currency);
      } catch (apiError) {
        this.errorHandlingService.handleExternalApiError(apiError, {
          ...context,
          metadata: { ...context.metadata, operation: 'fetchWallet' },
        });
      }

      return await this.walletRepository.createWallet(
        {
          id: result.id,
          currency: result.currency,
          balance: result.balance,
          depositAddress: result.deposit_address,
          convertedBalance: result.converted_balance,
          locked: result.locked,
          staked: result.staked,
          destinationTag: result.destination_tag,
        },
        userId,
      );
    } catch (error) {
      if (
        error instanceof BadRequestException ||
        error instanceof NotFoundException
      ) {
        throw error;
      }
      this.errorHandlingService.handleError(error, context, {
        customMessage: 'Failed to create wallet',
      });
    }
  }

  async validateCurrency(currency: string): Promise<void> {
    const context = this.errorHandlingService.createContext(
      'WalletService',
      'validateCurrency',
      undefined,
      { currency },
    );

    try {
      const currencies =
        await this.currencyRepository.findAllCurrencies(currency);
      if (currencies.length === 0) {
        throw new BadRequestException('Invalid currency');
      }
    } catch (error) {
      if (error instanceof BadRequestException) {
        throw error;
      }
      this.errorHandlingService.handleDatabaseError(error, context, {
        customMessage: 'Failed to validate currency',
      });
    }
  }

  async getUserWallet(walletId: string): Promise<Wallet> {
    const context = this.errorHandlingService.createContext(
      'WalletService',
      'getUserWallet',
      undefined,
      { walletId },
    );

    try {
      const wallet = await this.walletRepository.getUserWallet(walletId);
      if (!wallet) {
        throw new NotFoundException('Wallet not found');
      }
      return wallet;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.errorHandlingService.handleDatabaseError(error, context, {
        customMessage: 'Failed to fetch wallet',
      });
    }
  }

  async getAllUserActiveWallets(userId: string): Promise<Wallet[]> {
    const context = this.errorHandlingService.createContext(
      'WalletService',
      'getAllUserActiveWallets',
      userId,
    );

    try {
      return await this.walletRepository.getAllUserActiveWallets(userId);
    } catch (error) {
      this.errorHandlingService.handleDatabaseError(error, context, {
        customMessage: 'Failed to fetch user active wallets',
      });
    }
  }

  async findAllCurrencies(name?: string): Promise<Currency[]> {
    const context = this.errorHandlingService.createContext(
      'WalletService',
      'findAllCurrencies',
      undefined,
      { name },
    );

    try {
      // Check if the data is in the cache
      let cachedCurrencies: Currency[] = [];
      try {
        cachedCurrencies =
          await this.internalCacheService.getAllFromCollection<Currency>(
            'crypto:tag:currencies',
          );
        if (cachedCurrencies.length > 0 && !name) {
          return cachedCurrencies;
        }
      } catch (cacheError) {
        // Handle cache errors gracefully - continue with database query
        this.errorHandlingService.handleCacheError(cacheError, {
          ...context,
          metadata: { ...context.metadata, operation: 'getAllFromCollection' },
        });
      }

      // If not in cache, query the database
      const currencies = await this.currencyRepository.findAllCurrencies(name);
      return currencies;
    } catch (error) {
      this.errorHandlingService.handleDatabaseError(error, context, {
        customMessage: 'Failed to fetch currencies',
      });
    }
  }

  async updateWallet(
    walletId: string,
    updateData: Partial<Wallet>,
  ): Promise<Wallet> {
    const context = this.errorHandlingService.createContext(
      'WalletService',
      'updateWallet',
      undefined,
      { walletId, updateData },
    );

    try {
      const wallet = await this.walletRepository.getUserWallet(walletId);
      if (!wallet) {
        throw new NotFoundException('Wallet not found');
      }

      const updatedWallet = await this.walletRepository.updateWallet(
        walletId,
        updateData,
      );

      return updatedWallet;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.errorHandlingService.handleDatabaseError(error, context, {
        customMessage: 'Failed to update wallet',
      });
    }
  }

  async handleWalletUpdatedEvent(data: any): Promise<Wallet> {
    const context = this.errorHandlingService.createContext(
      'WalletService',
      'handleWalletUpdatedEvent',
      data?.user?.id,
      { currency: data?.currency },
    );

    try {
      const {
        currency,
        balance,
        locked,
        staked,
        user,
        converted_balance,
        deposit_address,
        destination_tag,
      } = data;

      const wallet = await this.walletRepository.findOne({
        where: {
          user: { id: user.id },
          currency: { currencyCode: currency },
        },
        relations: ['user', 'currency'],
      });

      if (!wallet) {
        // Fetch wallet from external API if not found locally
        let fetchWallet: any;
        try {
          fetchWallet = await this.quidaxService.fetchWallet(user.id, currency);
        } catch (apiError) {
          this.errorHandlingService.handleExternalApiError(apiError, {
            ...context,
            metadata: { ...context.metadata, operation: 'fetchWallet' },
          });
        }

        return await this.walletRepository.createWallet(
          {
            id: fetchWallet.id,
            currency: fetchWallet.currency,
            balance: fetchWallet.balance,
            depositAddress: fetchWallet.deposit_address,
            convertedBalance: fetchWallet.converted_balance,
            locked: fetchWallet.locked,
            staked: fetchWallet.staked,
            destinationTag: fetchWallet.destination_tag,
          },
          user.id,
        );
      }

      wallet.balance = balance;
      wallet.locked = locked;
      wallet.staked = staked;
      wallet.convertedBalance = converted_balance;

      if (deposit_address !== undefined) {
        wallet.depositAddress = deposit_address;
      }
      if (destination_tag !== undefined) {
        wallet.destinationTag = destination_tag;
      }

      return await this.walletRepository.save(wallet);
    } catch (error) {
      this.errorHandlingService.handleError(error, context, {
        customMessage: 'Failed to handle wallet updated event',
      });
    }
  }
}
