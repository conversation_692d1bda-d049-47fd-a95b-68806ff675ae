import { Injectable, NotFoundException } from '@nestjs/common';
import { UserRepository } from './users.repository';
import { WalletRepository } from './wallet.repository';
import { DataSource } from 'typeorm';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Transaction } from '../entities/transactions.entity';
import { TransactionDto } from '../dtos/transaction.dto';

@Injectable()
export class TransactionRepository extends TypeOrmRepository<Transaction> {
  constructor(
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
    private readonly walletRepository: WalletRepository,
  ) {
    super(Transaction, dataSource.createEntityManager());
  }

  async createTransaction(
    transactionDto: TransactionDto,
  ): Promise<Transaction> {
    const user = await this.userRepository.findOne({
      where: {
        userId: transactionDto.userId,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const wallet = await this.walletRepository.findOne({
      where: {
        user: { id: user.id },
      },
      relations: ['user', 'currency'],
    });

    if (!wallet) {
      throw new NotFoundException(
        'Wallet not found or does not belong to the user',
      );
    }

    const savedTransaction = await this.save({
      ...transactionDto,
      user,
      wallet,
      recipientAddress: transactionDto.address,
      recipientDestinationTag: transactionDto.destinationTag,
    });

    return savedTransaction;
  }

  async getTransaction(transactionId: string): Promise<Transaction> {
    const transaction = await this.findOne({
      where: {
        id: transactionId,
      },
      relations: ['user', 'wallet'],
    });

    if (!transaction) {
      throw new NotFoundException(
        `Transaction with ID ${transactionId} not found`,
      );
    }

    return transaction;
  }

  async getTransactionsByUser(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ transactions: Transaction[]; total: number }> {
    const query = this.createQueryBuilder('transaction')
      .where('transaction.userId = :userId', { userId })
      .andWhere('transaction.createdAt >= :startDate', { startDate })
      .andWhere('transaction.createdAt <= :endDate', { endDate })
      .orderBy('transaction.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('transaction.user', 'user')
      .leftJoinAndSelect('transaction.wallet', 'wallet');

    const [transactions, total] = await query.getManyAndCount();

    return { transactions, total };
  }

  async getTransactionsByWallet(
    walletId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ transactions: Transaction[]; total: number }> {
    const query = this.createQueryBuilder('transaction')
      .where('transaction.walletId = :walletId', { walletId })
      .andWhere('transaction.createdAt >= :startDate', { startDate })
      .andWhere('transaction.createdAt <= :endDate', { endDate })
      .orderBy('transaction.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('transaction.user', 'user')
      .leftJoinAndSelect('transaction.wallet', 'wallet');

    const [transactions, total] = await query.getManyAndCount();

    return { transactions, total };
  }
}
