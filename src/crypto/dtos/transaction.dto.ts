import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum } from 'class-validator';
import { TransactionType } from '../entities/transactions.entity';

// export class createTransactionDto {
//     @ApiProperty()
//     @IsString()

// }

export class TransactionDto {
  @IsString()
  reference: string | null;

  @IsString()
  currency: string;

  @IsString()
  amount: string;

  @IsString()
  @IsOptional()
  transactionNote: string;

  @IsString()
  @IsOptional()
  narration: string;

  @IsString()
  @IsOptional()
  userId?: string;

  @IsString()
  @IsOptional()
  address?: string;

  @IsString()
  @IsOptional()
  destinationTag?: string;

  @IsString()
  @IsOptional()
  network?: string;

  @IsEnum(TransactionType)
  @IsOptional()
  type?: TransactionType;
}

export class CreateTransactionDto {
  @ApiProperty()
  @IsString()
  amount: string;

  @ApiProperty({
    description: 'For external use and crediting only.',
    required: false,
  })
  @IsString()
  @IsOptional()
  fund_uid?: string;

  @ApiProperty({ description: 'For external use only.', required: false })
  @IsString()
  @IsOptional()
  fund_uid2?: string;

  @ApiProperty()
  @ApiProperty()
  @IsString()
  currency: string;

  @ApiProperty()
  @IsString()
  transaction_note: string;

  @ApiProperty()
  @IsString()
  narration: string;

  @ApiProperty()
  @IsString()
  @IsOptional()
  network: string;

  @ApiProperty()
  @IsString()
  pin: string;
}

export class FundTransactionDto {
  @ApiProperty()
  @IsString()
  amount: string;

  @ApiProperty()
  @ApiProperty()
  @IsString()
  currency: string;

  @ApiProperty()
  @IsString()
  transaction_note: string;

  @ApiProperty()
  @IsString()
  narration: string;
}
