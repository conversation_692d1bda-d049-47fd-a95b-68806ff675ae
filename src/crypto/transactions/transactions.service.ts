import {
  BadRequestException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import { TransactionRepository } from '../repositories/transaction.repository';
import {
  CreateTransactionDto,
  FundTransactionDto,
} from '../dtos/transaction.dto';
import { QuidaxService } from '@app/quidax';
import { AuthData } from '@crednet/authmanager';
import { randomUUID } from 'crypto';
import {
  RabbitmqService,
  PaymentCacheService,
  PaymentTransactionSource,
  PaymentTransactionWalletType,
  Currency,
  ReverseTransactionInterface,
  QueryTransactionDto,
  RequestWithdrawalDto,
  PaymentTransactionType,
} from '@crednet/utils';
import {
  Events,
  Exchanges,
  PaymentEvents,
  PaymentRequestEventTypes,
} from '../../utils/queue';
import {
  Transaction,
  TransactionStatus,
  TransactionType,
  WalletType,
} from '../entities/transactions.entity';
import { WalletRepository } from '../repositories/wallet.repository';
import { Queue } from 'bullmq';
import { InjectQueue } from '@nestjs/bullmq';
import { MoreThan } from 'typeorm';
import { ErrorHandlingService } from '../../utils/error-handling';

@Injectable()
export class TransactionsService {
  constructor(
    private readonly transactionRepository: TransactionRepository,
    private readonly quidaxService: QuidaxService,
    private readonly rmqService: RabbitmqService,
    private readonly walletRepository: WalletRepository,
    private readonly paymentCacheService: PaymentCacheService,
    @InjectQueue(Events.REQUERY_TRANSACTION)
    private readonly requeryTransactionQueue: Queue,
    private readonly errorHandlingService: ErrorHandlingService,
  ) {}

  async initfundCryptoWallet(
    createTransactionDto: FundTransactionDto,
    auth: AuthData,
  ) {
    const isKycVerified = true;
    if (!isKycVerified) {
      throw new BadRequestException('KYC not verified');
    }

    await this.throttleCall(
      auth.id.toString(),
      createTransactionDto.currency,
      TransactionType.FUND,
    );

    const reference = 'cp_trx_fnd_' + randomUUID();

    const masterAccount = await this.quidaxService.fetchWallet(
      'me',
      createTransactionDto.currency,
    );

    let currentExhangeRate;

    if (createTransactionDto.currency === 'ngn') {
      currentExhangeRate = {
        data: {
          to_amount: createTransactionDto.amount,
        },
      };
    } else {
      currentExhangeRate = await this.quidaxService.temporarySwapQuotation(
        'me',
        {
          from_currency: createTransactionDto.currency,
          to_currency: 'ngn',
          from_amount: createTransactionDto.amount,
        },
      );
    }

    const amount = currentExhangeRate.data.to_amount;

    if (parseFloat(masterAccount.balance) < parseFloat(amount)) {
      throw new BadRequestException('Cannot fund wallet');
    }

    // todo find out about withdrawal fees
    const transaction = await this.transactionRepository.createTransaction({
      amount: amount,
      currency: createTransactionDto.currency,
      type: TransactionType.FUND,
      transactionNote: createTransactionDto.transaction_note,
      narration: createTransactionDto.narration,
      reference,
      userId: auth.id.toString(),
    });

    return this.initiatePayment(transaction, auth.id.toString());
  }

  async initiatePayment(transaction: Transaction, userId: string) {
    this.paymentCacheService.savePayment({
      source: PaymentTransactionSource.CRYPTO_SERVICE,
      userId: userId,
      walletType:
        transaction.walletType == WalletType.credit
          ? PaymentTransactionWalletType.CREDPAL_CREDIT
          : PaymentTransactionWalletType.CREDPAL_CASH,
      currency: Currency.NGN,
      reference: transaction.reference,
      amount: +transaction.amount,
      description: `Fund ${transaction.currency.toUpperCase()} Crypto Wallet`,
      returningRoutingKey: PaymentEvents.FUND_PAYMENT_STATUS,
      meta: {
        reference: transaction.reference,
        amount: transaction.amount,
        currency: transaction.currency,
      },
    });

    await this.transactionRepository.update(
      { id: transaction.id },
      {
        paymentStatus: TransactionStatus.PROCESSING,
      },
    );

    return transaction;
  }

  async finalisePayment(transaction: Transaction) {
    await this.transactionRepository.update(
      { id: transaction.id },
      {
        paymentStatus: TransactionStatus.SUCCESS,
      },
    );
    try {
      const response = await this.quidaxService.transferCrypto('me', {
        amount: transaction.amount,
        currency: transaction.currency,
        fund_uid: transaction.user.id,
        narration: transaction.narration,
        transaction_note: transaction.transactionNote,
        reference: transaction.reference,
      });

      if (response.status == 'error') {
        await this.transactionRepository.update(
          { id: transaction.id },
          { errors: response },
        );
      }

      transaction.status = TransactionStatus.PROCESSING;
      transaction.txid = response.data.txid;
      transaction.fee = response.data.fee;
      transaction.total = response.data.total;
      transaction.reason = response.data.reason;
      transaction.recipientType = response.data.recipient.type;
      transaction.meta = response.data;

      await this.transactionRepository.save(transaction);
    } catch (error) {
      console.log(error);
      await this.transactionRepository.update(
        { id: transaction.id },
        { errors: error },
      );
    } finally {
      await this.transactionRepository.update(
        { id: transaction.id },
        { status: TransactionStatus.PROCESSING },
      );
    }
    this.requeryTransactionQueue.add(
      'requery_transaction',
      {
        reference: transaction.reference,
      },
      {
        lifo: false,
        attempts: 1,
        backoff: { type: 'exponential', delay: 2000 },
        jobId: randomUUID(),
        removeOnComplete: true,
        removeOnFail: false,
        delay: 10000,
      },
    );
  }

  async requery(reference: string): Promise<any> {
    const transaction = await this.transactionRepository.findOne({
      where: { reference },
      relations: ['user', 'wallet'],
    });

    if (!transaction || transaction.status == TransactionStatus.SUCCESS) {
      return;
    }

    await this.verifyTransaction(reference);
  }

  async verifyTransaction(reference: string) {
    const context = this.errorHandlingService.createContext(
      'TransactionsService',
      'verifyTransaction',
      undefined,
      { reference },
    );

    try {
      const data = await this.quidaxService.fetchWithdrawalsByReference(
        'me',
        reference,
      );

      // Log verification result for debugging
      this.errorHandlingService.handleError(
        new Error(`Withdrawal verification result: ${JSON.stringify(data)}`),
        context,
        { shouldThrow: false, logLevel: 'debug' },
      );

      switch (data?.status?.toLowerCase()) {
        case 'rejected':
          await this.handleError(reference, data, data?.status === 'rejected');
          break;
        case 'done':
          await this.handleSuccess(reference, data);
          break;
        default:
          // Log unknown status for investigation
          this.errorHandlingService.handleError(
            new Error(`Unknown transaction status: ${data?.status}`),
            context,
            { shouldThrow: false, logLevel: 'warn' },
          );
          break;
      }
    } catch (error) {
      this.errorHandlingService.handleError(error, context, {
        shouldThrow: false,
        customMessage: 'Failed to verify transaction with external service',
      });
      await this.handleError(reference, error);
    }
  }

  async handleError(reference: string, error: any, updateStatus = false) {
    const context = this.errorHandlingService.createContext(
      'TransactionsService',
      'handleError',
      undefined,
      { reference, updateStatus },
    );

    try {
      const update = {
        errors: typeof error === 'string' ? { message: error } : error,
        status: TransactionStatus.FAILED,
      };

      const transaction = await this.transactionRepository.findOne({
        where: { reference },
        relations: ['user'],
      });

      if (!transaction) {
        this.errorHandlingService.handleError(
          new Error(`Transaction not found for reference: ${reference}`),
          context,
          { shouldThrow: false },
        );
        return;
      }

      if (updateStatus) {
        this.errorHandlingService.handleError(
          error,
          { ...context, userId: transaction.user?.userId },
          {
            shouldThrow: false,
            logLevel: 'warn',
            customMessage: `Transaction ${reference} failed and requires status update`,
          },
        );

        if (transaction.type === TransactionType.FUND) {
          await this.refundTransaction(reference);
        } else {
          // TODO: notify user that the transaction failed
        }
      }

      await this.transactionRepository.update({ reference }, update);
    } catch (dbError) {
      this.errorHandlingService.handleDatabaseError(dbError, context, {
        shouldThrow: false,
        customMessage: 'Failed to update transaction error status',
      });
    }
  }

  async handleSuccess(reference: string, data: any) {
    await this.transactionRepository.update(
      { reference },
      { meta: data, status: TransactionStatus.SUCCESS },
    );

    //todo handle exchange

    const txn = await this.transactionRepository.findOne({
      where: {
        reference,
      },
      relations: ['user', 'wallet'],
    });

    if (txn.type === TransactionType.WITHDRAWAL) {
      await this.rmqService.send(Exchanges.PAYMENT, {
        key: PaymentEvents.TOP_UP,
        data: {
          transaction: {
            source: PaymentTransactionSource.CRYPTO_SERVICE,
            userId: txn.user.userId,
            currency: Currency.NGN,
            amount: parseFloat(txn.amount),
            type: PaymentTransactionType.CREDIT,
            description: 'Crypto Withdrawal',
            meta: txn.meta,
            reference: txn.reference,
            returningRoutingKey: PaymentEvents.PROCESS_WITHDRAWAL,
          },
          returningRoutingKey: PaymentEvents.PROCESS_WITHDRAWAL,
        } as RequestWithdrawalDto,
      });
    }

    console.log(txn);
    // TODO: Send notification to user when notification service is implemented, if it is a fund transaction
  }

  async requeryProcessing(page: number) {
    console.log('running job:: requeryProcesing ', page);
    // Get funding transactions
    const fundingItems = await this.transactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          type: TransactionType.FUND,
          status: TransactionStatus.PROCESSING,
          paymentStatus: TransactionStatus.SUCCESS,
        },
      },
    );

    // Get withdrawal transactions
    const withdrawalItems = await this.transactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          type: TransactionType.WITHDRAWAL,
          status: TransactionStatus.PROCESSING,
          paymentStatus: TransactionStatus.PENDING,
        },
      },
    );

    const externalItems = await this.transactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          type: TransactionType.EXTERNAL,
          status: TransactionStatus.PROCESSING,
          paymentStatus: TransactionStatus.EXTERNAL,
        },
      },
    );

    // Combine results
    const items = {
      items: [
        ...fundingItems.items,
        ...withdrawalItems.items,
        ...externalItems.items,
      ],
      meta: {
        totalPages: Math.max(
          fundingItems.meta.totalPages,
          withdrawalItems.meta.totalPages,
          externalItems.meta.totalPages,
        ),
      },
    };

    // Optionally log with a structured logger, e.g.:
    // this.logger.debug(`Items found: ${items.items.length}`);

    for (const item of items.items) {
      console.log('running job:: requeryProcesing ', item.reference);
      try {
        await this.requery(item.reference);
      } catch (error) {
        console.log(error);
      }
    }

    if (page < items.meta.totalPages) {
      return this.requeryProcessing(++page);
    }
  }

  async handleRefund(page: number) {
    console.log('running job:: handleRefund ', page);
    const items = await this.transactionRepository.findMany(
      {
        page,
        limit: 100,
      },
      {
        where: {
          status: TransactionStatus.FAILED,
          paymentStatus: TransactionStatus.SUCCESS,
          isRefunded: false,
        },
      },
    );

    console.log(items.items.length);

    for (const item of items.items) {
      console.log('running job:: handleRefund ', item.reference);

      this.refundTransaction(item.reference);
    }

    if (page < items.meta.totalPages) {
      return this.handleRefund(++page);
    }
  }

  async refundTransaction(reference: string) {
    console.log('refundTransaction ', reference);

    this.rmqService.send(Exchanges.PAYMENT, {
      key: PaymentRequestEventTypes.REVERSE_TRANSACTION,
      data: {
        source: PaymentTransactionSource.CRYPTO_SERVICE,
        reference,
        reason: 'Transaction failed from provider',
        returningRoutingKey: PaymentEvents.FUND_PAYMENT_STATUS,
      } as ReverseTransactionInterface,
    });
  }

  async reprocessPendingProcessing(page: number) {
    console.log('running job:: reprocessPendingProcessing ', page);
    const items = await this.transactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          status: TransactionStatus.PENDING,
          paymentStatus: TransactionStatus.SUCCESS,
        },
        relations: ['user', 'wallet'],
      },
    );

    console.log(items.items.length);

    for (const item of items.items) {
      await this.finalisePayment(item);
    }

    if (page < items.meta.totalPages) {
      return this.reprocessPendingProcessing(++page);
    }
  }

  async requeryPendingPayment(page: number) {
    console.log('running job:: requeryPendingPayment ', page);
    const items = await this.transactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          paymentStatus: TransactionStatus.PROCESSING,
        },
      },
    );

    for (const item of items.items) {
      if (item.type == TransactionType.FUND) {
        this.rmqService.send(Exchanges.PAYMENT, {
          key: PaymentRequestEventTypes.QUERY_TRANSACTION,
          data: {
            returningRoutingKey: PaymentEvents.FUND_PAYMENT_STATUS,
            reference: item.reference,
          } as QueryTransactionDto,
        });
      }
      if (item.type == TransactionType.WITHDRAWAL) {
        this.rmqService.send(Exchanges.PAYMENT, {
          key: PaymentRequestEventTypes.QUERY_TRANSACTION,
          data: {
            returningRoutingKey: PaymentEvents.PROCESS_WITHDRAWAL,
            reference: item.reference,
          } as QueryTransactionDto,
        });
      }
    }

    if (page < items.meta.totalPages) {
      return this.requeryPendingPayment(++page);
    }
  }

  async requeryPendingWithdrawal(page: number) {
    console.log('running job:: requeryPendingWithdrawal ', page);
    const items = await this.transactionRepository.findMany(
      {
        page,
        limit: 10,
      },
      {
        where: {
          status: TransactionStatus.SUCCESS,
          paymentStatus: TransactionStatus.PENDING,
          type: TransactionType.WITHDRAWAL,
        },
      },
    );

    for (const item of items.items) {
      await this.rmqService.send(Exchanges.PAYMENT, {
        key: PaymentEvents.TOP_UP,
        data: {
          transaction: {
            source: PaymentTransactionSource.CRYPTO_SERVICE,
            userId: item.user.userId,
            currency: Currency.NGN,
            amount: parseFloat(item.amount),
            type: PaymentTransactionType.CREDIT,
            description: 'Crypto Withdrawal',
            meta: item.meta,
            reference: item.reference,
            returningRoutingKey: PaymentEvents.PROCESS_WITHDRAWAL,
          },
          returningRoutingKey: PaymentEvents.PROCESS_WITHDRAWAL,
        } as RequestWithdrawalDto,
      });
    }

    if (page < items.meta.totalPages) {
      return this.requeryPendingWithdrawal(++page);
    }
  }

  async transferToExternalWallet(
    createTransactionDto: CreateTransactionDto,
    auth: AuthData,
  ) {
    try {
      const reference = 'cp_trx_ext_' + randomUUID();

      const getUserWallet =
        await this.walletRepository.getUserWalletByAuthIdAndCurrency(
          auth.id.toString(),
          createTransactionDto.currency,
        );

      if (
        parseFloat(getUserWallet.balance) <
        parseFloat(createTransactionDto.amount)
      ) {
        throw new ForbiddenException('Insufficient funds');
      }

      await this.throttleCall(
        auth.id.toString(),
        createTransactionDto.currency,
        TransactionType.EXTERNAL,
      );

      const response = await this.quidaxService.transferCrypto(
        getUserWallet.user.id,
        {
          amount: createTransactionDto.amount,
          currency: createTransactionDto.currency,
          fund_uid: createTransactionDto.fund_uid,
          fund_uid2: createTransactionDto.fund_uid2,
          narration: createTransactionDto.narration,
          transaction_note: createTransactionDto.transaction_note,
          network: createTransactionDto.network,
          reference,
        },
      );

      if (response.status == 'error') {
        console.error(response);

        throw new BadRequestException(response.message);
      }

      const transaction = await this.transactionRepository.createTransaction({
        amount: createTransactionDto.amount,
        currency: createTransactionDto.currency,
        type: TransactionType.EXTERNAL,
        transactionNote: createTransactionDto.transaction_note,
        narration: createTransactionDto.narration,
        address: createTransactionDto.fund_uid,
        destinationTag: createTransactionDto.fund_uid2,
        network: createTransactionDto.network,
        reference,
        userId: auth.id.toString(),
      });

      transaction.paymentStatus = TransactionStatus.EXTERNAL;
      transaction.txid = response.data.txid;
      transaction.fee = response.data.fee;
      transaction.total = response.data.total;
      transaction.reason = response.data.reason;
      transaction.recipientType = response.data.recipient.type;
      transaction.meta = response.data;

      await this.transactionRepository.save(transaction);

      this.requeryTransactionQueue.add(
        'requery_transaction',
        {
          reference: transaction.reference,
        },
        {
          lifo: false,
          attempts: 1,
          backoff: { type: 'exponential', delay: 2000 },
          jobId: randomUUID(),
          removeOnComplete: true,
          removeOnFail: false,
          delay: 10000,
        },
      );

      return transaction;
    } catch (error) {
      console.error(error);
      throw error;
    }
  }

  async throttleCall(
    userId: string,
    currency: string,
    type: TransactionType,
    minute: number = 2,
  ) {
    // reject if successful creation txn was made at most 2 minutes ago
    const minutesAgo = new Date(Date.now() - minute * 60 * 1000); // Calculate the time 2 minutes ago and reduce by 1 hr because of timezone

    const pastTxn = await this.transactionRepository.findOne({
      where: {
        user: { userId: userId },
        currency: currency,
        // status: TransactionStatus.SUCCESS,
        type,
        createdAt: MoreThan(minutesAgo),
      },
      order: {
        createdAt: 'DESC',
      },
    });

    if (pastTxn) {
      throw new BadRequestException(
        `${type} transaction recently called, please wait ${minute} minutes and try again`,
      );
    }
  }

  async initiateWithdrawal(
    createTransactionDto: FundTransactionDto,
    auth: AuthData,
  ) {
    try {
      const isKycVerified = true;
      if (!isKycVerified) {
        throw new BadRequestException('KYC not verified');
      }

      const reference = 'cp_trx_wdr_' + randomUUID();

      await this.throttleCall(
        auth.id.toString(),
        createTransactionDto.currency,
        TransactionType.WITHDRAWAL,
      );
      const wallet = await this.walletRepository.findOne({
        where: {
          user: { userId: auth.id.toString() },
          currency: { currencyCode: createTransactionDto.currency },
        },
      });

      if (
        parseFloat(wallet.balance) < parseFloat(createTransactionDto.amount)
      ) {
        throw new BadRequestException('Insufficient funds');
      }

      const currentExhangeRate =
        await this.quidaxService.temporarySwapQuotation('me', {
          from_currency: createTransactionDto.currency,
          to_currency: 'ngn',
          from_amount: createTransactionDto.amount,
        });

      const amount = currentExhangeRate.data.to_amount;

      const masterAccount = await this.quidaxService.fetchWallet(
        'me',
        createTransactionDto.currency,
      );

      const masterAccountId = masterAccount.id;

      const transaction = await this.transactionRepository.createTransaction({
        amount: amount,
        currency: createTransactionDto.currency,
        type: TransactionType.WITHDRAWAL,
        transactionNote: createTransactionDto.transaction_note,
        narration: createTransactionDto.narration,
        address: masterAccountId,
        reference,
        userId: auth.id.toString(),
      });

      const response = await this.quidaxService.transferCrypto(wallet.user.id, {
        amount: amount,
        currency: createTransactionDto.currency,
        fund_uid: masterAccountId,
        narration: createTransactionDto.narration,
        transaction_note: createTransactionDto.transaction_note,
        reference,
      });

      if (response.status == 'error') {
        console.error(response);

        throw new BadRequestException(response.message);
      }

      transaction.txid = response.data.txid;
      transaction.status = TransactionStatus.PROCESSING;
      transaction.fee = response.data.fee;
      transaction.total = response.data.total;
      transaction.reason = response.data.reason;
      transaction.recipientType = response.data.recipient.type;
      transaction.meta = response.data;

      await this.transactionRepository.save(transaction);

      this.requeryTransactionQueue.add(
        'requery_transaction',
        {
          reference: transaction.reference,
        },
        {
          lifo: false,
          attempts: 1,
          backoff: { type: 'exponential', delay: 2000 },
          jobId: randomUUID(),
          removeOnComplete: true,
          removeOnFail: false,
          delay: 10000,
        },
      );

      return transaction;
    } catch (error) {
      console.error(error);
    }
  }
}
