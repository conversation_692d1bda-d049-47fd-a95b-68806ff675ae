import { Entity, Column, ManyToOne } from 'typeorm';
import { User } from './user.entity';
import { Wallet } from './wallet.entity';
import { BaseEntity } from '../../config/repository/base-entity';

export enum DepositStatus {
  SUBMITTED = 'submitted',
  ACCEPTED = 'accepted',
  ON_HOLD = 'on_hold',
  FAILED_AML = 'failed_aml',
  CHECKED = 'checked',
}

export enum PaymentStatus {
  CONFIRMED = 'confirmed',
  UNCONFIRMED = 'unconfirmed',
}

@Entity('deposits')
export class Deposit extends BaseEntity {
  @Column()
  type: string;

  @Column()
  currency: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  amount: string;

  @Column({ type: 'decimal', precision: 20, scale: 8 })
  fee: string;

  @Column()
  txid: string;

  @Column({
    type: 'enum',
    enum: DepositStatus,
    default: DepositStatus.SUBMITTED,
  })
  status: DepositStatus;

  @Column({ nullable: true })
  reason?: string;

  @ManyToOne(() => Wallet, (wallet) => wallet.id)
  wallet: Wallet;

  @ManyToOne(() => User, (user) => user.id)
  user: User;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.UNCONFIRMED,
  })
  paymentStatus: PaymentStatus;

  @Column({ type: 'int', nullable: true })
  confirmations?: number;

  @Column({ type: 'int', nullable: true })
  requiredConfirmations?: number;

  @Column({ nullable: true })
  paymentAddress?: string;

  @Column({ nullable: true })
  paymentReference?: string;

  @Column({ nullable: true })
  paymentNetwork?: string;

  @Column({ nullable: true })
  sender?: string;
}
