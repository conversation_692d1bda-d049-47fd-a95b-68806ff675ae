import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { typeOrmConfig } from './config/data-source';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ScheduleModule } from '@nestjs/schedule';
import { BullModule } from '@nestjs/bullmq';
import config from './config';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { Events, Exchanges, PaymentEvents } from './utils/queue';
import { InternalCacheModule } from '@app/internal-cache';
import { QuidaxModule } from '../libs/quidax/src';
import { UsersModule } from './crypto/users/users.module';
import { SeederModule } from './seeder/seeder.module';
import { WalletModule } from './crypto/wallet/wallet.module';
import { MarketsModule } from './crypto/markets/markets.module';
import { RabbitmqModule, RedisModule } from '@crednet/utils';
import { TransactionsModule } from './crypto/transactions/transactions.module';
import { OrdersModule } from './crypto/orders/orders.module';
import { TradesModule } from './crypto/trades/trades.module';
import { DepositsModule } from './crypto/deposits/deposits.module';
import { SwapsModule } from './crypto/swaps/swaps.module';
import { CacheManagerModule } from '@crednet/authmanager';
import { ErrorHandlingModule } from './utils/error-handling';

@Module({
  imports: [
    ErrorHandlingModule,
    QuidaxModule,
    TypeOrmModule.forRoot(typeOrmConfig),
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot({
      global: true,
    }),
    RedisModule.forRoot({
      redis: {
        url: config.redis.url,
      },
    }),
    InternalCacheModule,
    CacheManagerModule,
    RabbitmqModule.register({
      host: config.rabbitMq.brockers[0],
      queueName: process.env.RMQ_QUEUE_NAME,
      prefetchCount: 5,
      showLog: false,
      consumeDeadLetterQueue: true,
      deadLetterQueueInterval: 60000,
      producer: {
        name: process.env.RMQ_EXCHANGE,
        durable: true,
      },
      subscriptions: [
        `${Exchanges.WEBHOOK}.${Events.CREATE_WALLET}`,
        `${Exchanges.WEBHOOK}.${Events.WALLET_UPDATED}`,
        `${Exchanges.WEBHOOK}.${Events.ORDER_DONE}`,
        `${Exchanges.WEBHOOK}.${Events.ORDER_CANCELLED}`,
        `${Exchanges.WEBHOOK}.${Events.DEPOSIT_COMPLETED}`,
        `${Exchanges.WEBHOOK}.${Events.DEPOSIT_FAILED_AML}`,
        `${Exchanges.WEBHOOK}.${Events.DEPOSIT_ON_HOLD}`,
        `${Exchanges.WEBHOOK}.${Events.DEPOSIT_SUCCESSFUL}`,
        `${Exchanges.WEBHOOK}.${Events.WITHDRAWAL_SUCCESS}`,
        `${Exchanges.WEBHOOK}.${Events.WITHDRAWAL_FAILED}`,
        `${Exchanges.WEBHOOK}.${Events.SWAP_TRANSACTION_COMPLETED}`,
        `${Exchanges.WEBHOOK}.${Events.SWAP_TRANSACTION_FAILED}`,
        `${Exchanges.WEBHOOK}.${Events.SWAP_TRANSACTION_REVERSED}`,
        `${Exchanges.PAYMENT}.${PaymentEvents.FUND_PAYMENT_STATUS}`,
      ],
    }),
    BullModule.forRoot({
      connection: {
        username: config.redis.user,
        password: config.redis.password,
        host: config.redis.host,
        port: config.redis.port,
      },
      defaultJobOptions: {
        removeOnComplete: {
          age: 3600,
        },
        removeOnFail: {
          age: 48 * 3600,
        },
        attempts: 5,
      },
    }),
    UsersModule,
    SeederModule,
    WalletModule,
    MarketsModule,
    TransactionsModule,
    OrdersModule,
    TradesModule,
    DepositsModule,
    SwapsModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
